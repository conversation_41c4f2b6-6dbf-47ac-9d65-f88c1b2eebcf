<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Quiz Arcade - Test</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Rubik+Mono+One&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #ff00ff;
            --secondary: #00ffff;
            --accent: #ffcc00;
            --bg: #1a1a2e;
        }
        
        body {
            background: var(--bg);
            color: white;
            min-height: 100vh;
            font-family: 'Press Start 2P', cursive;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .arcade-screen {
            background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.3);
            border-radius: 0.5rem;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            text-align: center;
            border: 2px solid var(--primary);
        }
        
        .neon-text {
            color: var(--primary);
            text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 20px var(--primary), 0 0 40px var(--primary);
            font-family: 'Rubik Mono One', sans-serif;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .arcade-button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem 2rem;
            margin: 0.5rem;
            border-radius: 0.5rem;
            font-family: 'Press Start 2P', cursive;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(255,255,255,0.2);
        }
        
        .arcade-button:hover {
            background: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255,255,255,0.4);
        }
        
        .hidden {
            display: none;
        }
        
        .category-select {
            background: black;
            color: white;
            border: 2px solid var(--secondary);
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Press Start 2P', cursive;
            font-size: 0.8rem;
            width: 100%;
            max-width: 400px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Landing Screen -->
        <div id="landing-screen" class="arcade-screen">
            <h1 class="neon-text">
                <i class="fas fa-gamepad"></i>
                RETRO QUIZ ARCADE
                <i class="fas fa-bolt"></i>
            </h1>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Test your knowledge in this electrifying trivia challenge!</p>
            
            <div>
                <button onclick="showScreen('setup-screen')" class="arcade-button">
                    START GAME
                </button>
                <button onclick="showScreen('join-screen')" class="arcade-button">
                    JOIN GAME
                </button>
                <button onclick="showScreen('leaderboard-screen')" class="arcade-button">
                    LEADERBOARD
                </button>
            </div>
        </div>
        
        <!-- Setup Screen -->
        <div id="setup-screen" class="hidden arcade-screen">
            <button onclick="showScreen('landing-screen')" style="position: absolute; top: 1rem; left: 1rem; background: #666; padding: 0.5rem 1rem; border: none; border-radius: 0.5rem; color: white; font-family: 'Press Start 2P', cursive; cursor: pointer;">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <h2 class="neon-text" style="font-size: 2rem;">GAME SETUP</h2>
            <p style="font-size: 1rem; margin-bottom: 2rem;">Customize your quiz experience</p>
            
            <div style="margin-bottom: 2rem;">
                <h3 style="color: var(--primary); margin-bottom: 1rem;">CATEGORY</h3>
                <select id="category-select" class="category-select">
                    <option value="general">🧠 General Knowledge</option>
                    <option value="science">🔬 Science & Nature</option>
                    <option value="history">🏛️ History</option>
                    <option value="movies">🎬 Movies</option>
                    <option value="videogames">🎮 Video Games</option>
                    <option value="music">🎵 Music</option>
                    <option value="soccer">⚽ Soccer</option>
                    <option value="afrobeats">🎶 Afrobeats</option>
                    <option value="nigeria">🇳🇬 Nigerian History & Politics</option>
                </select>
            </div>
            
            <button onclick="startQuiz()" class="arcade-button" style="font-size: 1.2rem; padding: 1.5rem 3rem;">
                START QUIZ
            </button>
        </div>
        
        <!-- Quiz Screen -->
        <div id="quiz-screen" class="hidden arcade-screen">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <div style="font-size: 1rem;">
                    QUESTION <span id="current-question">1</span>/<span id="total-questions">10</span>
                </div>
                <div style="font-size: 1rem;">
                    SCORE: <span id="current-score">0</span>
                </div>
            </div>
            
            <div style="background: rgba(0,0,0,0.3); padding: 2rem; margin-bottom: 2rem; border-radius: 0.5rem; border: 2px solid var(--primary); min-height: 100px; display: flex; align-items: center; justify-content: center;">
                <h3 id="question-text" style="font-size: 1.2rem; text-align: center;">Loading question...</h3>
            </div>
            
            <div id="answers-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 2rem;">
                <!-- Answers will be inserted here by JavaScript -->
            </div>
        </div>
        
        <!-- Results Screen -->
        <div id="results-screen" class="hidden arcade-screen">
            <h2 class="neon-text" style="font-size: 3rem; margin-bottom: 2rem;">GAME OVER!</h2>
            
            <div style="background: rgba(0,0,0,0.5); padding: 2rem; border-radius: 0.5rem; border: 2px solid var(--secondary); max-width: 400px; margin: 0 auto 2rem;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                    <div>
                        <div style="font-size: 0.8rem;">SCORE</div>
                        <div id="final-score" style="font-size: 2rem; color: var(--primary);">850</div>
                    </div>
                    <div>
                        <div style="font-size: 0.8rem;">CORRECT</div>
                        <div id="correct-answers" style="font-size: 2rem; color: var(--secondary);">8/10</div>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <i class="fas fa-trophy" style="font-size: 3rem; color: gold; margin: 1rem;"></i>
                </div>
            </div>
            
            <div>
                <button onclick="startQuiz()" class="arcade-button">
                    PLAY AGAIN
                </button>
                <button onclick="showScreen('landing-screen')" class="arcade-button">
                    MAIN MENU
                </button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // Fallback initialization if script.js doesn't load
        if (typeof showScreen === 'undefined') {
            function showScreen(screenId) {
                // Hide all screens
                const screens = ['landing-screen', 'setup-screen', 'quiz-screen', 'results-screen'];
                screens.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.classList.add('hidden');
                });
                
                // Show requested screen
                const targetScreen = document.getElementById(screenId);
                if (targetScreen) targetScreen.classList.remove('hidden');
            }
            
            function startQuiz() {
                alert('Quiz functionality loading... Please refresh the page.');
            }
            
            console.log('Fallback functions loaded');
        }
    </script>
</body>
</html>
