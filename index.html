<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Quiz Arcade</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Rubik+Mono+One&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes flicker {
            0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
                text-shadow:
                    0 0 5px #fff,
                    0 0 10px #fff,
                    0 0 20px #0fa,
                    0 0 40px #0fa,
                    0 0 80px #0fa;
            }
            20%, 24%, 55% {        
                text-shadow: none;
            }
        }
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.8);
            }
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) rotate(-2deg); }
            20%, 40%, 60%, 80% { transform: translateX(5px) rotate(2deg); }
        }
        @keyframes progress-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        @keyframes explode {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }
        .animate-progress {
            animation: progress-pulse 1.5s infinite;
        }
        .neon-text {
            animation: flicker 3s infinite alternate;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-stroke: 1px rgba(255,255,255,0.5);
            -webkit-text-stroke: 1px rgba(255,255,255,0.5);
        }
        .pulse-button {
            animation: pulse 2s infinite;
        }
        .float {
            animation: float 3s ease-in-out infinite;
        }
        .shake {
            animation: shake 0.5s;
        }
        .pixel-border {
            border-style: solid;
            border-width: 4px;
            border-image: repeating-linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff, #00ffff) 10;
        }
        .arcade-screen {
            background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.3);
        }
        .theme-neon {
            --primary: #ff00ff;
            --secondary: #00ffff;
            --accent: #ffcc00;
            --bg: #1a1a2e;
        }
        .theme-pixel {
            --primary: #ff3366;
            --secondary: #66ff33;
            --accent: #3366ff;
            --bg: #222;
        }
        .theme-synth {
            --primary: #ff6b6b;
            --secondary: #4ecdc4;
            --accent: #ffe66d;
            --bg: #0f0c29;
        }
        .answer-correct {
            background-color: rgba(0, 255, 0, 0.3);
            border: 2px solid #00ff00;
            animation: pulse 0.5s 3, float 1s ease-in-out;
            box-shadow: 0 0 20px #00ff00;
        }
        .answer-wrong {
            background-color: rgba(255, 0, 0, 0.3);
            border: 2px solid #ff0000;
            animation: shake 0.5s, pulse 0.5s 3;
            box-shadow: 0 0 20px #ff0000;
        }
        .arcade-button {
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(255,255,255,0.2);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        .arcade-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            transition: all 0.5s;
        }
        .arcade-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255,255,255,0.4);
        }
        .arcade-button:hover::before {
            left: 100%;
        }
        .arcade-button:active {
            transform: translateY(1px);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring__circle {
            transition: stroke-dashoffset 0.5s;
            stroke-linecap: round;
        }
        .pixel-font {
            font-family: 'Press Start 2P', cursive;
        }
        .retro-font {
            font-family: 'Rubik Mono One', sans-serif;
        }

        /* Enhanced Graphics */
        @keyframes matrix-rain {
            0% { transform: translateY(-100vh); opacity: 1; }
            100% { transform: translateY(100vh); opacity: 0; }
        }
        @keyframes hologram {
            0%, 100% {
                transform: skew(0deg, 0deg);
                filter: hue-rotate(0deg);
            }
            25% {
                transform: skew(1deg, 0deg);
                filter: hue-rotate(90deg);
            }
            50% {
                transform: skew(0deg, 1deg);
                filter: hue-rotate(180deg);
            }
            75% {
                transform: skew(-1deg, 0deg);
                filter: hue-rotate(270deg);
            }
        }
        @keyframes scan-lines {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100vh); }
        }
        @keyframes glow-pulse {
            0%, 100% {
                box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
            }
            50% {
                box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary), 0 0 40px var(--primary);
            }
        }
        @keyframes particle-float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }
        @keyframes lightning {
            0%, 90%, 100% { opacity: 0; }
            5%, 85% { opacity: 1; }
        }

        .matrix-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
            z-index: 1;
        }
        .matrix-char {
            position: absolute;
            color: var(--primary);
            font-family: 'Press Start 2P', monospace;
            font-size: 12px;
            animation: matrix-rain 3s linear infinite;
            opacity: 0.3;
        }
        .hologram-effect {
            animation: hologram 4s infinite;
        }
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--secondary), transparent);
            animation: scan-lines 2s linear infinite;
            opacity: 0.5;
        }
        .glow-border {
            animation: glow-pulse 2s infinite;
            border: 2px solid var(--primary);
        }
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent);
            border-radius: 50%;
            animation: particle-float 4s infinite;
        }
        .lightning-effect {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, transparent, var(--secondary), transparent);
            animation: lightning 3s infinite;
        }
        .category-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 1.2em;
            animation: float 2s ease-in-out infinite;
        }
    </style>
</head>
<body class="theme-neon bg-[var(--bg)] min-h-screen text-white transition-colors duration-500">
    <div id="app" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Landing Screen -->
        <div id="landing-screen" class="arcade-screen w-full max-w-4xl rounded-lg p-8 text-center relative overflow-hidden glow-border">
            <div class="absolute inset-0 bg-black opacity-20"></div>
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[var(--primary)] to-[var(--secondary)]"></div>

            <!-- Matrix Rain Effect -->
            <div class="matrix-bg" id="matrix-container"></div>

            <!-- Scan Lines -->
            <div class="scan-line" style="animation-delay: 0s;"></div>
            <div class="scan-line" style="animation-delay: 1s;"></div>

            <!-- Lightning Effects -->
            <div class="lightning-effect" style="left: 10%; animation-delay: 0.5s;"></div>
            <div class="lightning-effect" style="right: 15%; animation-delay: 2s;"></div>

            <!-- Floating Particles -->
            <div class="particle" style="top: 20%; left: 80%; animation-delay: 0s;"></div>
            <div class="particle" style="top: 60%; left: 10%; animation-delay: 1s;"></div>
            <div class="particle" style="top: 40%; right: 20%; animation-delay: 2s;"></div>
            <div class="particle" style="bottom: 30%; left: 30%; animation-delay: 1.5s;"></div>
            <div class="relative z-10">
                <h1 class="neon-text retro-font text-5xl md:text-7xl mb-6 hologram-effect">
                    <i class="fas fa-gamepad category-icon"></i>
                    RETRO QUIZ ARCADE
                    <i class="fas fa-bolt category-icon"></i>
                </h1>
                <p class="pixel-font text-xl mb-12 hologram-effect">Test your knowledge in this electrifying trivia challenge!</p>
                
                <div class="flex flex-col md:flex-row justify-center gap-6 mb-12">
                    <button onclick="showScreen('setup-screen')" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        START GAME
                    </button>
                    <button onclick="showScreen('join-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        JOIN GAME
                    </button>
                    <button onclick="showScreen('leaderboard-screen')" class="arcade-button pixel-font bg-[var(--accent)] hover:bg-[var(--primary)] text-black py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        LEADERBOARD
                    </button>
                </div>
                
                <div class="flex justify-center gap-4 mb-4">
                    <span class="pixel-font">THEME:</span>
                    <button onclick="setTheme('neon')" class="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-blue-500"></button>
                    <button onclick="setTheme('pixel')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-500 to-green-500"></button>
                    <button onclick="setTheme('synth')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-400 to-teal-400"></button>
                </div>
            </div>
            
            <div class="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                <div class="w-3 h-3 rounded-full bg-[var(--primary)] animate-pulse"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--secondary)] animate-pulse delay-100"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--accent)] animate-pulse delay-200"></div>
            </div>
        </div>

        <!-- Setup Screen -->
        <div id="setup-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>

            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">GAME SETUP</h2>
                <p class="pixel-font text-lg">Customize your quiz experience</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">CATEGORY</h3>
                    <select id="category-select" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]">
                        <option value="general">General Knowledge</option>
                        <option value="science">Science & Nature</option>
                        <option value="history">History</option>
                        <option value="movies">Movies</option>
                        <option value="videogames">Video Games</option>
                        <option value="music">Music</option>
                        <option value="soccer">⚽ Soccer</option>
                    </select>
                </div>

                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--secondary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--secondary)]">DIFFICULTY</h3>
                    <div class="flex justify-between">
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" checked class="form-radio h-5 w-5 text-[var(--primary)]">
                            <span class="ml-2 pixel-font">Easy</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--secondary)]">
                            <span class="ml-2 pixel-font">Medium</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--accent)]">
                            <span class="ml-2 pixel-font">Hard</span>
                        </label>
                    </div>
                </div>

                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--accent)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--accent)]">QUESTIONS</h3>
                    <input type="range" min="5" max="20" value="10" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-[var(--primary)]">
                    <div class="flex justify-between mt-2">
                        <span class="pixel-font">5</span>
                        <span class="pixel-font">10</span>
                        <span class="pixel-font">15</span>
                        <span class="pixel-font">20</span>
                    </div>
                </div>

                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">TIME PER QUESTION</h3>
                    <div class="flex items-center gap-4">
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">-</button>
                        <span class="pixel-font text-2xl">15</span>
                        <span class="pixel-font">seconds</span>
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">+</button>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button onclick="startQuiz()" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-12 rounded-lg text-2xl transition-all duration-300 transform hover:scale-105">
                    START QUIZ
                </button>
            </div>
        </div>

        <!-- Quiz Screen -->
        <div id="quiz-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden glow-border">
            <div class="flex justify-between items-center mb-8">
                <div class="pixel-font text-lg">
                    QUESTION <span id="current-question">1</span>/<span id="total-questions">10</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 relative">
                        <svg class="progress-ring w-8 h-8" viewBox="0 0 36 36">
                            <circle class="progress-ring__circle" stroke="var(--primary)" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                        </svg>
                        <span id="time-left" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pixel-font text-xs">15</span>
                    </div>
                    <span class="pixel-font">SCORE: <span id="current-score">0</span></span>
                </div>
            </div>

            <div id="question-container" class="bg-black bg-opacity-30 p-6 mb-8 rounded-lg border-2 border-[var(--primary)] min-h-32 flex items-center justify-center">
                <h3 id="question-text" class="pixel-font text-xl md:text-2xl text-center">Loading question...</h3>
            </div>

            <div id="answers-container" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <!-- Answers will be inserted here by JavaScript -->
            </div>

            <div class="w-full bg-gray-800 rounded-full h-4 mb-4 overflow-hidden">
                <div id="progress-bar" class="bg-[var(--primary)] h-4 rounded-full relative" style="width: 0%">
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-30 animate-progress"></div>
                </div>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden text-center glow-border">
            <div class="matrix-bg" id="results-matrix"></div>

            <div class="relative z-10">
                <h2 class="neon-text retro-font text-5xl mb-6">GAME OVER!</h2>

                <div class="bg-black bg-opacity-50 p-8 rounded-lg border-2 border-[var(--secondary)] max-w-md mx-auto mb-8">
                    <div class="flex justify-between mb-4">
                        <div>
                            <div class="pixel-font text-sm">SCORE</div>
                            <div id="final-score" class="text-4xl font-bold text-[var(--primary)]">850</div>
                        </div>
                        <div>
                            <div class="pixel-font text-sm">CORRECT</div>
                            <div id="correct-answers" class="text-4xl font-bold text-[var(--secondary)]">8/10</div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="pixel-font text-sm">TIME</div>
                        <div id="time-taken" class="text-xl text-[var(--accent)]">2:15</div>
                    </div>
                </div>

                <div class="flex flex-col md:flex-row justify-center gap-6">
                    <button onclick="startQuiz()" class="arcade-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        PLAY AGAIN
                    </button>
                    <button onclick="showScreen('landing-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        MAIN MENU
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
