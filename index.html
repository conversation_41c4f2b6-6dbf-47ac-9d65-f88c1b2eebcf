<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Quiz Arcade</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Rubik+Mono+One&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes flicker {
            0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
                text-shadow:
                    0 0 5px #fff,
                    0 0 10px #fff,
                    0 0 20px #0fa,
                    0 0 40px #0fa,
                    0 0 80px #0fa;
            }
            20%, 24%, 55% {        
                text-shadow: none;
            }
        }
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.8);
            }
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) rotate(-2deg); }
            20%, 40%, 60%, 80% { transform: translateX(5px) rotate(2deg); }
        }
        @keyframes progress-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        @keyframes explode {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }
        .animate-progress {
            animation: progress-pulse 1.5s infinite;
        }
        .neon-text {
            animation: flicker 3s infinite alternate;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-stroke: 1px rgba(255,255,255,0.5);
            -webkit-text-stroke: 1px rgba(255,255,255,0.5);
        }
        .pulse-button {
            animation: pulse 2s infinite;
        }
        .float {
            animation: float 3s ease-in-out infinite;
        }
        .shake {
            animation: shake 0.5s;
        }
        .pixel-border {
            border-style: solid;
            border-width: 4px;
            border-image: repeating-linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff, #00ffff) 10;
        }
        .arcade-screen {
            background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.3);
        }
        .theme-neon {
            --primary: #ff00ff;
            --secondary: #00ffff;
            --accent: #ffcc00;
            --bg: #1a1a2e;
        }
        .theme-pixel {
            --primary: #ff3366;
            --secondary: #66ff33;
            --accent: #3366ff;
            --bg: #222;
        }
        .theme-synth {
            --primary: #ff6b6b;
            --secondary: #4ecdc4;
            --accent: #ffe66d;
            --bg: #0f0c29;
        }
        .answer-correct {
            background-color: rgba(0, 255, 0, 0.3);
            border: 2px solid #00ff00;
            animation: pulse 0.5s 3, float 1s ease-in-out;
            box-shadow: 0 0 20px #00ff00;
        }
        .answer-wrong {
            background-color: rgba(255, 0, 0, 0.3);
            border: 2px solid #ff0000;
            animation: shake 0.5s, pulse 0.5s 3;
            box-shadow: 0 0 20px #ff0000;
        }
        .arcade-button {
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(255,255,255,0.2);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        .arcade-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            transition: all 0.5s;
        }
        .arcade-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255,255,255,0.4);
        }
        .arcade-button:hover::before {
            left: 100%;
        }
        .arcade-button:active {
            transform: translateY(1px);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring__circle {
            transition: stroke-dashoffset 0.5s;
            stroke-linecap: round;
        }
        .pixel-font {
            font-family: 'Press Start 2P', cursive;
        }
        .retro-font {
            font-family: 'Rubik Mono One', sans-serif;
        }

        /* Enhanced Graphics */
        @keyframes matrix-rain {
            0% { transform: translateY(-100vh); opacity: 1; }
            100% { transform: translateY(100vh); opacity: 0; }
        }
        @keyframes hologram {
            0%, 100% {
                transform: skew(0deg, 0deg);
                filter: hue-rotate(0deg);
            }
            25% {
                transform: skew(1deg, 0deg);
                filter: hue-rotate(90deg);
            }
            50% {
                transform: skew(0deg, 1deg);
                filter: hue-rotate(180deg);
            }
            75% {
                transform: skew(-1deg, 0deg);
                filter: hue-rotate(270deg);
            }
        }
        @keyframes scan-lines {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100vh); }
        }
        @keyframes glow-pulse {
            0%, 100% {
                box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
            }
            50% {
                box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary), 0 0 40px var(--primary);
            }
        }
        @keyframes particle-float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }
        @keyframes lightning {
            0%, 90%, 100% { opacity: 0; }
            5%, 85% { opacity: 1; }
        }

        .matrix-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
            z-index: 1;
        }
        .matrix-char {
            position: absolute;
            color: var(--primary);
            font-family: 'Press Start 2P', monospace;
            font-size: 12px;
            animation: matrix-rain 3s linear infinite;
            opacity: 0.3;
        }
        .hologram-effect {
            animation: hologram 4s infinite;
        }
        .scan-line {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--secondary), transparent);
            animation: scan-lines 2s linear infinite;
            opacity: 0.5;
        }
        .glow-border {
            animation: glow-pulse 2s infinite;
            border: 2px solid var(--primary);
        }
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent);
            border-radius: 50%;
            animation: particle-float 4s infinite;
        }
        .lightning-effect {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, transparent, var(--secondary), transparent);
            animation: lightning 3s infinite;
        }
        .category-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 1.2em;
            animation: float 2s ease-in-out infinite;
        }
    </style>
</head>
<body class="theme-neon bg-[var(--bg)] min-h-screen text-white transition-colors duration-500">
    <div id="app" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Landing Screen -->
        <div id="landing-screen" class="arcade-screen w-full max-w-4xl rounded-lg p-8 text-center relative overflow-hidden glow-border">
            <div class="absolute inset-0 bg-black opacity-20"></div>
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[var(--primary)] to-[var(--secondary)]"></div>

            <!-- Matrix Rain Effect -->
            <div class="matrix-bg" id="matrix-container"></div>

            <!-- Scan Lines -->
            <div class="scan-line" style="animation-delay: 0s;"></div>
            <div class="scan-line" style="animation-delay: 1s;"></div>

            <!-- Lightning Effects -->
            <div class="lightning-effect" style="left: 10%; animation-delay: 0.5s;"></div>
            <div class="lightning-effect" style="right: 15%; animation-delay: 2s;"></div>

            <!-- Floating Particles -->
            <div class="particle" style="top: 20%; left: 80%; animation-delay: 0s;"></div>
            <div class="particle" style="top: 60%; left: 10%; animation-delay: 1s;"></div>
            <div class="particle" style="top: 40%; right: 20%; animation-delay: 2s;"></div>
            <div class="particle" style="bottom: 30%; left: 30%; animation-delay: 1.5s;"></div>
            <div class="relative z-10">
                <h1 class="neon-text retro-font text-5xl md:text-7xl mb-6 hologram-effect">
                    <i class="fas fa-gamepad category-icon"></i>
                    RETRO QUIZ ARCADE
                    <i class="fas fa-bolt category-icon"></i>
                </h1>
                <p class="pixel-font text-xl mb-12 hologram-effect">Test your knowledge in this electrifying trivia challenge!</p>
                
                <div class="flex flex-col md:flex-row justify-center gap-6 mb-12">
                    <button onclick="showScreen('setup-screen')" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        START GAME
                    </button>
                    <button onclick="showScreen('join-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        JOIN GAME
                    </button>
                    <button onclick="showScreen('leaderboard-screen')" class="arcade-button pixel-font bg-[var(--accent)] hover:bg-[var(--primary)] text-black py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        LEADERBOARD
                    </button>
                </div>
                
                <div class="flex justify-center gap-4 mb-4">
                    <span class="pixel-font">THEME:</span>
                    <button onclick="setTheme('neon')" class="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-blue-500"></button>
                    <button onclick="setTheme('pixel')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-500 to-green-500"></button>
                    <button onclick="setTheme('synth')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-400 to-teal-400"></button>
                </div>
            </div>
            
            <div class="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                <div class="w-3 h-3 rounded-full bg-[var(--primary)] animate-pulse"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--secondary)] animate-pulse delay-100"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--accent)] animate-pulse delay-200"></div>
            </div>
        </div>
        
        <!-- Setup Screen -->
        <div id="setup-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">GAME SETUP</h2>
                <p class="pixel-font text-lg">Customize your quiz experience</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">CATEGORY</h3>
                    <select id="category-select" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]">
                        <option value="general">General Knowledge</option>
                        <option value="science">Science & Nature</option>
                        <option value="history">History</option>
                        <option value="movies">Movies</option>
                        <option value="videogames">Video Games</option>
                        <option value="music">Music</option>
                        <option value="soccer">⚽ Soccer</option>
                    </select>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--secondary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--secondary)]">DIFFICULTY</h3>
                    <div class="flex justify-between">
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" checked class="form-radio h-5 w-5 text-[var(--primary)]">
                            <span class="ml-2 pixel-font">Easy</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--secondary)]">
                            <span class="ml-2 pixel-font">Medium</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--accent)]">
                            <span class="ml-2 pixel-font">Hard</span>
                        </label>
                    </div>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--accent)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--accent)]">QUESTIONS</h3>
                    <input type="range" min="5" max="20" value="10" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-[var(--primary)]">
                    <div class="flex justify-between mt-2">
                        <span class="pixel-font">5</span>
                        <span class="pixel-font">10</span>
                        <span class="pixel-font">15</span>
                        <span class="pixel-font">20</span>
                    </div>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">TIME PER QUESTION</h3>
                    <div class="flex items-center gap-4">
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">-</button>
                        <span class="pixel-font text-2xl">15</span>
                        <span class="pixel-font">seconds</span>
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">+</button>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button onclick="startQuiz()" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-12 rounded-lg text-2xl transition-all duration-300 transform hover:scale-105">
                    START QUIZ
                </button>
            </div>
        </div>
        
        <!-- Join Screen -->
        <div id="join-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">JOIN GAME</h2>
                <p class="pixel-font text-lg">Enter a game code to join</p>
            </div>
            
            <div class="max-w-md mx-auto bg-black bg-opacity-30 p-8 rounded-lg border-2 border-[var(--secondary)]">
                <div class="mb-6">
                    <label class="block pixel-font text-lg mb-2 text-[var(--primary)]">GAME CODE</label>
                    <input type="text" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]" placeholder="Enter 6-digit code">
                </div>
                
                <div class="mb-6">
                    <label class="block pixel-font text-lg mb-2 text-[var(--primary)]">PLAYER NAME</label>
                    <input type="text" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]" placeholder="Your nickname">
                </div>
                
                <button class="w-full arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                    JOIN NOW
                </button>
            </div>
        </div>
        
        <!-- Quiz Screen -->
        <div id="quiz-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden glow-border">
            <!-- Background Effects -->
            <div class="scan-line" style="animation-delay: 0.5s;"></div>
            <div class="lightning-effect" style="right: 5%; animation-delay: 1s;"></div>
            <div class="particle" style="top: 10%; right: 10%; animation-delay: 0.5s;"></div>
            <div class="particle" style="bottom: 20%; left: 15%; animation-delay: 1.5s;"></div>
            <div class="flex justify-between items-center mb-8">
                <div class="pixel-font text-lg">
                    QUESTION <span id="current-question">1</span>/<span id="total-questions">10</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 relative">
                        <svg class="progress-ring w-8 h-8" viewBox="0 0 36 36">
                            <circle class="progress-ring__circle" stroke="var(--primary)" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                        </svg>
                        <span id="time-left" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pixel-font text-xs">15</span>
                    </div>
                    <span class="pixel-font">SCORE: <span id="current-score">0</span></span>
                </div>
            </div>
            
            <div id="question-container" class="bg-black bg-opacity-30 p-6 mb-8 rounded-lg border-2 border-[var(--primary)] min-h-32 flex items-center justify-center">
                <h3 id="question-text" class="pixel-font text-xl md:text-2xl text-center">Loading question...</h3>
            </div>
            
            <div id="answers-container" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <!-- Answers will be inserted here by JavaScript -->
            </div>
            
            <div class="w-full bg-gray-800 rounded-full h-4 mb-4 overflow-hidden">
                <div id="progress-bar" class="bg-[var(--primary)] h-4 rounded-full relative" style="width: 0%">
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-30 animate-progress"></div>
                </div>
            </div>
        </div>
        
        <!-- Results Screen -->
        <div id="results-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden text-center glow-border">
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-full h-full bg-[radial-gradient(circle,var(--primary),transparent)] opacity-20"></div>
            </div>

            <!-- Enhanced Background Effects -->
            <div class="matrix-bg" id="results-matrix"></div>
            <div class="scan-line" style="animation-delay: 0s;"></div>
            <div class="scan-line" style="animation-delay: 1.5s;"></div>
            <div class="lightning-effect" style="left: 5%; animation-delay: 0.3s;"></div>
            <div class="lightning-effect" style="right: 8%; animation-delay: 2s;"></div>

            <!-- Celebration Particles -->
            <div class="particle" style="top: 15%; left: 20%; animation-delay: 0s; background: gold;"></div>
            <div class="particle" style="top: 25%; right: 25%; animation-delay: 0.5s; background: silver;"></div>
            <div class="particle" style="bottom: 30%; left: 30%; animation-delay: 1s; background: #ff6b6b;"></div>
            <div class="particle" style="bottom: 40%; right: 20%; animation-delay: 1.5s; background: #4ecdc4;"></div>
            <div class="particle" style="top: 60%; left: 10%; animation-delay: 2s; background: #ffe66d;"></div>
            
            <div class="relative z-10">
                <h2 class="neon-text retro-font text-5xl mb-6">GAME OVER!</h2>
                
                <div class="bg-black bg-opacity-50 p-8 rounded-lg border-2 border-[var(--secondary)] max-w-md mx-auto mb-8">
                    <div class="flex justify-between mb-4">
                        <div>
                            <div class="pixel-font text-sm">SCORE</div>
                            <div id="final-score" class="text-4xl font-bold text-[var(--primary)]">850</div>
                        </div>
                        <div>
                            <div class="pixel-font text-sm">CORRECT</div>
                            <div id="correct-answers" class="text-4xl font-bold text-[var(--secondary)]">8/10</div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="pixel-font text-sm">TIME</div>
                        <div id="time-taken" class="text-xl text-[var(--accent)]">2:15</div>
                    </div>
                    
                    <div class="flex justify-center gap-4 mt-6 relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-32 h-32 rounded-full bg-[var(--primary)] opacity-10 animate-ping absolute"></div>
                        </div>
                        <div class="float z-10">
                            <i class="fas fa-trophy text-4xl text-yellow-400 drop-shadow-lg"></i>
                        </div>
                        <div class="float animation-delay-200 z-10">
                            <i class="fas fa-medal text-4xl text-blue-400 drop-shadow-lg"></i>
                        </div>
                        <div class="float animation-delay-400 z-10">
                            <i class="fas fa-star text-4xl text-pink-400 drop-shadow-lg"></i>
                        </div>
                    </div>
                    <div class="absolute inset-0 overflow-hidden">
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                    </div>
                </div>
                
                <div class="flex flex-col md:flex-row justify-center gap-6">
                    <button onclick="startQuiz()" class="arcade-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        PLAY AGAIN
                    </button>
                    <button onclick="showScreen('leaderboard-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        LEADERBOARD
                    </button>
                    <button class="arcade-button pixel-font bg-[var(--accent)] hover:bg-[var(--primary)] text-black py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        SHARE SCORE
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Leaderboard Screen -->
        <div id="leaderboard-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">LEADERBOARD</h2>
                <p class="pixel-font text-lg">Top players this week</p>
            </div>
            
            <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                <div class="grid grid-cols-12 gap-4 pixel-font mb-4 pb-2 border-b-2 border-[var(--secondary)]">
                    <div class="col-span-1">#</div>
                    <div class="col-span-6">PLAYER</div>
                    <div class="col-span-3">SCORE</div>
                    <div class="col-span-2">DATE</div>
                </div>
                
                <div class="space-y-3">
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--primary)] bg-opacity-20 p-2 rounded">
                        <div class="col-span-1 text-yellow-400">1</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-crown text-yellow-400"></i>
                            <span>RetroGamer88</span>
                        </div>
                        <div class="col-span-3">980</div>
                        <div class="col-span-2 text-sm">Today</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--secondary)] bg-opacity-10 p-2 rounded">
                        <div class="col-span-1 text-gray-300">2</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-medal text-gray-300"></i>
                            <span>PixelMaster</span>
                        </div>
                        <div class="col-span-3">950</div>
                        <div class="col-span-2 text-sm">Yesterday</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--accent)] bg-opacity-10 p-2 rounded">
                        <div class="col-span-1 text-amber-600">3</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-medal text-amber-600"></i>
                            <span>NeonNinja</span>
                        </div>
                        <div class="col-span-3">920</div>
                        <div class="col-span-2 text-sm">2 days ago</div>
                    </div>
                    
                    <!-- More leaderboard entries -->
                    <div class="grid grid-cols-12 gap-4 items-center p-2 rounded hover:bg-black hover:bg-opacity-20">
                        <div class="col-span-1">4</div>
                        <div class="col-span-6">QuizWizard</div>
                        <div class="col-span-3">890</div>
                        <div class="col-span-2 text-sm">3 days ago</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center p-2 rounded hover:bg-black hover:bg-opacity-20">
                        <div class="col-span-1">5</div>
                        <div class="col-span-6">ArcadeAce</div>
                        <div class="col-span-3">870</div>
                        <div class="col-span-2 text-sm">4 days ago</div>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 flex justify-center">
                <button onclick="showScreen('landing-screen')" class="arcade-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                    MAIN MENU
                </button>
            </div>
        </div>
    </div>

    <script>
        // Question Database
        const questionDatabase = {
            general: [
                { question: "What is the capital of Australia?", answers: ["Sydney", "Melbourne", "Canberra", "Perth"], correct: 2 },
                { question: "Which planet is known as the Red Planet?", answers: ["Venus", "Mars", "Jupiter", "Saturn"], correct: 1 },
                { question: "What is the largest ocean on Earth?", answers: ["Atlantic", "Indian", "Arctic", "Pacific"], correct: 3 },
                { question: "Who painted the Mona Lisa?", answers: ["Vincent van Gogh", "Pablo Picasso", "Leonardo da Vinci", "Michelangelo"], correct: 2 },
                { question: "What is the smallest country in the world?", answers: ["Monaco", "Vatican City", "San Marino", "Liechtenstein"], correct: 1 },
                { question: "Which element has the chemical symbol 'O'?", answers: ["Gold", "Oxygen", "Silver", "Iron"], correct: 1 },
                { question: "What is the tallest mountain in the world?", answers: ["K2", "Kangchenjunga", "Mount Everest", "Lhotse"], correct: 2 },
                { question: "Which continent is the largest by area?", answers: ["Africa", "North America", "Asia", "Europe"], correct: 2 },
                { question: "What is the currency of Japan?", answers: ["Yuan", "Won", "Yen", "Rupee"], correct: 2 },
                { question: "Who wrote 'Romeo and Juliet'?", answers: ["Charles Dickens", "William Shakespeare", "Jane Austen", "Mark Twain"], correct: 1 },
                { question: "What is the hardest natural substance?", answers: ["Gold", "Iron", "Diamond", "Platinum"], correct: 2 },
                { question: "Which country has the most time zones?", answers: ["Russia", "USA", "China", "Canada"], correct: 0 },
                { question: "What is the largest mammal in the world?", answers: ["African Elephant", "Blue Whale", "Giraffe", "Polar Bear"], correct: 1 },
                { question: "Which gas makes up most of Earth's atmosphere?", answers: ["Oxygen", "Carbon Dioxide", "Nitrogen", "Hydrogen"], correct: 2 },
                { question: "What is the longest river in the world?", answers: ["Amazon", "Nile", "Mississippi", "Yangtze"], correct: 1 },
                { question: "Which country invented pizza?", answers: ["Greece", "Italy", "France", "Spain"], correct: 1 },
                { question: "What is the speed of light?", answers: ["300,000 km/s", "150,000 km/s", "450,000 km/s", "600,000 km/s"], correct: 0 },
                { question: "Which organ in the human body produces insulin?", answers: ["Liver", "Kidney", "Pancreas", "Heart"], correct: 2 },
                { question: "What is the most spoken language in the world?", answers: ["English", "Spanish", "Mandarin Chinese", "Hindi"], correct: 2 },
                { question: "Which planet is closest to the Sun?", answers: ["Venus", "Mercury", "Earth", "Mars"], correct: 1 },
                { question: "What is the largest desert in the world?", answers: ["Sahara", "Gobi", "Antarctica", "Arabian"], correct: 2 },
                { question: "Who invented the telephone?", answers: ["Thomas Edison", "Alexander Graham Bell", "Nikola Tesla", "Benjamin Franklin"], correct: 1 },
                { question: "What is the chemical formula for water?", answers: ["CO2", "H2O", "NaCl", "CH4"], correct: 1 },
                { question: "Which country is known as the Land of the Rising Sun?", answers: ["China", "South Korea", "Japan", "Thailand"], correct: 2 },
                { question: "What is the largest bone in the human body?", answers: ["Skull", "Femur", "Spine", "Ribcage"], correct: 1 }
            ],
            science: [
                { question: "What is the atomic number of carbon?", answers: ["4", "6", "8", "12"], correct: 1 },
                { question: "Which scientist developed the theory of relativity?", answers: ["Isaac Newton", "Albert Einstein", "Galileo Galilei", "Stephen Hawking"], correct: 1 },
                { question: "What is the powerhouse of the cell?", answers: ["Nucleus", "Ribosome", "Mitochondria", "Endoplasmic Reticulum"], correct: 2 },
                { question: "Which gas is most abundant in Earth's atmosphere?", answers: ["Oxygen", "Carbon Dioxide", "Nitrogen", "Argon"], correct: 2 },
                { question: "What is the study of earthquakes called?", answers: ["Geology", "Seismology", "Meteorology", "Oceanography"], correct: 1 },
                { question: "Which planet has the most moons?", answers: ["Jupiter", "Saturn", "Uranus", "Neptune"], correct: 1 },
                { question: "What is the pH of pure water?", answers: ["6", "7", "8", "9"], correct: 1 },
                { question: "Which blood type is known as the universal donor?", answers: ["A", "B", "AB", "O"], correct: 3 },
                { question: "What is the smallest unit of matter?", answers: ["Molecule", "Atom", "Electron", "Proton"], correct: 1 },
                { question: "Which force keeps planets in orbit around the Sun?", answers: ["Magnetic force", "Gravitational force", "Nuclear force", "Electric force"], correct: 1 },
                { question: "What is the chemical symbol for gold?", answers: ["Go", "Gd", "Au", "Ag"], correct: 2 },
                { question: "Which organ is responsible for filtering blood?", answers: ["Liver", "Lungs", "Kidneys", "Heart"], correct: 2 },
                { question: "What is the speed of sound in air at room temperature?", answers: ["343 m/s", "300 m/s", "400 m/s", "500 m/s"], correct: 0 },
                { question: "Which scientist is known for the laws of motion?", answers: ["Albert Einstein", "Isaac Newton", "Galileo Galilei", "Johannes Kepler"], correct: 1 },
                { question: "What is the most abundant element in the universe?", answers: ["Oxygen", "Carbon", "Hydrogen", "Helium"], correct: 2 },
                { question: "Which part of the brain controls balance?", answers: ["Cerebrum", "Cerebellum", "Brainstem", "Hippocampus"], correct: 1 },
                { question: "What is the chemical formula for methane?", answers: ["CH4", "CO2", "H2O", "NH3"], correct: 0 },
                { question: "Which type of radiation has the shortest wavelength?", answers: ["Radio waves", "Microwaves", "X-rays", "Gamma rays"], correct: 3 },
                { question: "What is the process by which plants make food?", answers: ["Respiration", "Photosynthesis", "Transpiration", "Germination"], correct: 1 },
                { question: "Which planet is known for its prominent ring system?", answers: ["Jupiter", "Saturn", "Uranus", "Neptune"], correct: 1 },
                { question: "What is the hardest substance known to science?", answers: ["Diamond", "Graphene", "Tungsten", "Titanium"], correct: 0 },
                { question: "Which scientist discovered penicillin?", answers: ["Louis Pasteur", "Alexander Fleming", "Marie Curie", "Gregor Mendel"], correct: 1 },
                { question: "What is the study of heredity called?", answers: ["Genetics", "Evolution", "Ecology", "Anatomy"], correct: 0 },
                { question: "Which gas is produced during photosynthesis?", answers: ["Carbon Dioxide", "Nitrogen", "Oxygen", "Hydrogen"], correct: 2 },
                { question: "What is the largest organ in the human body?", answers: ["Liver", "Brain", "Lungs", "Skin"], correct: 3 }
            ],
            history: [
                { question: "In which year did World War II end?", answers: ["1944", "1945", "1946", "1947"], correct: 1 },
                { question: "Who was the first President of the United States?", answers: ["Thomas Jefferson", "John Adams", "George Washington", "Benjamin Franklin"], correct: 2 },
                { question: "Which ancient wonder of the world was located in Alexandria?", answers: ["Hanging Gardens", "Lighthouse", "Colossus", "Mausoleum"], correct: 1 },
                { question: "The Berlin Wall fell in which year?", answers: ["1987", "1988", "1989", "1990"], correct: 2 },
                { question: "Who was known as the 'Iron Lady'?", answers: ["Angela Merkel", "Margaret Thatcher", "Golda Meir", "Indira Gandhi"], correct: 1 },
                { question: "Which empire was ruled by Julius Caesar?", answers: ["Greek", "Roman", "Byzantine", "Ottoman"], correct: 1 },
                { question: "The French Revolution began in which year?", answers: ["1789", "1799", "1779", "1769"], correct: 0 },
                { question: "Who painted the ceiling of the Sistine Chapel?", answers: ["Leonardo da Vinci", "Raphael", "Michelangelo", "Donatello"], correct: 2 },
                { question: "Which country gifted the Statue of Liberty to the USA?", answers: ["Britain", "France", "Spain", "Italy"], correct: 1 },
                { question: "The Titanic sank in which year?", answers: ["1910", "1911", "1912", "1913"], correct: 2 },
                { question: "Who was the first man to walk on the moon?", answers: ["Buzz Aldrin", "Neil Armstrong", "John Glenn", "Alan Shepard"], correct: 1 },
                { question: "Which war was fought between the North and South in America?", answers: ["Revolutionary War", "Civil War", "War of 1812", "Spanish-American War"], correct: 1 },
                { question: "The Great Wall of China was built to defend against which people?", answers: ["Mongols", "Japanese", "Russians", "Koreans"], correct: 0 },
                { question: "Who wrote the Communist Manifesto?", answers: ["Vladimir Lenin", "Karl Marx", "Joseph Stalin", "Leon Trotsky"], correct: 1 },
                { question: "Which Egyptian queen was known for her relationships with Julius Caesar and Mark Antony?", answers: ["Nefertiti", "Hatshepsut", "Cleopatra", "Ankhesenamun"], correct: 2 },
                { question: "The Renaissance began in which country?", answers: ["France", "Germany", "Italy", "Spain"], correct: 2 },
                { question: "Who was the British Prime Minister during most of World War II?", answers: ["Neville Chamberlain", "Winston Churchill", "Clement Attlee", "Anthony Eden"], correct: 1 },
                { question: "The Hundred Years' War was fought between which two countries?", answers: ["England and France", "Spain and Portugal", "Germany and Austria", "Italy and Greece"], correct: 0 },
                { question: "Which explorer is credited with discovering America?", answers: ["Vasco da Gama", "Christopher Columbus", "Ferdinand Magellan", "Marco Polo"], correct: 1 },
                { question: "The Black Death occurred in which century?", answers: ["13th", "14th", "15th", "16th"], correct: 1 },
                { question: "Who was the last Tsar of Russia?", answers: ["Alexander III", "Nicholas II", "Alexander II", "Nicholas I"], correct: 1 },
                { question: "The Magna Carta was signed in which year?", answers: ["1205", "1215", "1225", "1235"], correct: 1 },
                { question: "Which ancient civilization built Machu Picchu?", answers: ["Aztec", "Maya", "Inca", "Olmec"], correct: 2 },
                { question: "The Cold War was primarily between which two superpowers?", answers: ["USA and China", "USA and USSR", "Britain and Germany", "France and Russia"], correct: 1 },
                { question: "Who was assassinated in Sarajevo in 1914, sparking World War I?", answers: ["Kaiser Wilhelm", "Archduke Franz Ferdinand", "Tsar Nicholas", "King George V"], correct: 1 }
            ],
            movies: [
                { question: "Which movie won the Academy Award for Best Picture in 2023?", answers: ["Top Gun: Maverick", "Everything Everywhere All at Once", "The Banshees of Inisherin", "Avatar: The Way of Water"], correct: 1 },
                { question: "Who directed the movie 'Inception'?", answers: ["Steven Spielberg", "Christopher Nolan", "Martin Scorsese", "Quentin Tarantino"], correct: 1 },
                { question: "Which actor played the Joker in 'The Dark Knight'?", answers: ["Joaquin Phoenix", "Jack Nicholson", "Heath Ledger", "Jared Leto"], correct: 2 },
                { question: "What is the highest-grossing film of all time?", answers: ["Titanic", "Avatar", "Avengers: Endgame", "Star Wars: The Force Awakens"], correct: 2 },
                { question: "Which movie features the song 'My Heart Will Go On'?", answers: ["The Bodyguard", "Titanic", "Ghost", "Dirty Dancing"], correct: 1 },
                { question: "Who played Forrest Gump?", answers: ["Tom Cruise", "Tom Hanks", "Brad Pitt", "Leonardo DiCaprio"], correct: 1 },
                { question: "Which movie is known for the quote 'May the Force be with you'?", answers: ["Star Trek", "Star Wars", "Guardians of the Galaxy", "Interstellar"], correct: 1 },
                { question: "Who directed 'Pulp Fiction'?", answers: ["Martin Scorsese", "Quentin Tarantino", "David Fincher", "Paul Thomas Anderson"], correct: 1 },
                { question: "Which movie won the first ever Academy Award for Best Picture?", answers: ["Wings", "Sunrise", "The Jazz Singer", "7th Heaven"], correct: 0 },
                { question: "Who played Neo in 'The Matrix'?", answers: ["Will Smith", "Keanu Reeves", "Johnny Depp", "Brad Pitt"], correct: 1 },
                { question: "Which Disney movie features the song 'Let It Go'?", answers: ["Moana", "Frozen", "Tangled", "Encanto"], correct: 1 },
                { question: "Who directed 'Jaws'?", answers: ["George Lucas", "Steven Spielberg", "Francis Ford Coppola", "Martin Scorsese"], correct: 1 },
                { question: "Which movie features a young Macaulay Culkin?", answers: ["The Good Son", "Home Alone", "My Girl", "Uncle Buck"], correct: 1 },
                { question: "Who played the main character in 'Gladiator'?", answers: ["Brad Pitt", "Russell Crowe", "Mel Gibson", "Kevin Costner"], correct: 1 },
                { question: "Which movie is set in the fictional country of Wakanda?", answers: ["Black Panther", "Captain America", "Thor", "Iron Man"], correct: 0 },
                { question: "Who directed 'The Godfather'?", answers: ["Martin Scorsese", "Francis Ford Coppola", "Robert De Niro", "Al Pacino"], correct: 1 },
                { question: "Which movie features the character Jack Sparrow?", answers: ["Treasure Island", "Pirates of the Caribbean", "Master and Commander", "The Count of Monte Cristo"], correct: 1 },
                { question: "Who played the main character in 'Rocky'?", answers: ["Robert De Niro", "Al Pacino", "Sylvester Stallone", "Arnold Schwarzenegger"], correct: 2 },
                { question: "Which movie is known for the quote 'Here's looking at you, kid'?", answers: ["Gone with the Wind", "Casablanca", "Roman Holiday", "Sunset Boulevard"], correct: 1 },
                { question: "Who directed 'E.T. the Extra-Terrestrial'?", answers: ["George Lucas", "Steven Spielberg", "Ridley Scott", "James Cameron"], correct: 1 },
                { question: "Which movie features the song 'Somewhere Over the Rainbow'?", answers: ["Singin' in the Rain", "The Wizard of Oz", "Mary Poppins", "The Sound of Music"], correct: 1 },
                { question: "Who played the main character in 'The Terminator'?", answers: ["Sylvester Stallone", "Arnold Schwarzenegger", "Bruce Willis", "Jean-Claude Van Damme"], correct: 1 },
                { question: "Which movie won the Academy Award for Best Picture in 2020?", answers: ["1917", "Joker", "Parasite", "Once Upon a Time in Hollywood"], correct: 2 },
                { question: "Who directed 'Avatar'?", answers: ["Steven Spielberg", "James Cameron", "Ridley Scott", "Peter Jackson"], correct: 1 },
                { question: "Which movie features the character Hannibal Lecter?", answers: ["Seven", "The Silence of the Lambs", "Zodiac", "Shutter Island"], correct: 1 }
            ],
            videogames: [
                { question: "Which game features the character Master Chief?", answers: ["Call of Duty", "Halo", "Gears of War", "Destiny"], correct: 1 },
                { question: "What is the best-selling video game of all time?", answers: ["Tetris", "Minecraft", "Grand Theft Auto V", "Super Mario Bros."], correct: 1 },
                { question: "Which company created the PlayStation?", answers: ["Nintendo", "Microsoft", "Sony", "Sega"], correct: 2 },
                { question: "In which game do you play as Link?", answers: ["Super Mario", "The Legend of Zelda", "Metroid", "Donkey Kong"], correct: 1 },
                { question: "Which game popularized the battle royale genre?", answers: ["Fortnite", "PUBG", "Apex Legends", "Call of Duty: Warzone"], correct: 1 },
                { question: "What is the main character's name in the Grand Theft Auto: Vice City?", answers: ["Carl Johnson", "Niko Bellic", "Tommy Vercetti", "Claude"], correct: 2 },
                { question: "Which game features the Umbrella Corporation?", answers: ["Silent Hill", "Resident Evil", "Dead Space", "The Last of Us"], correct: 1 },
                { question: "What year was the original Super Mario Bros. released?", answers: ["1983", "1985", "1987", "1989"], correct: 1 },
                { question: "Which game is known for the phrase 'The cake is a lie'?", answers: ["Half-Life", "Portal", "BioShock", "System Shock"], correct: 1 },
                { question: "Who is the main antagonist in the Super Mario series?", answers: ["Wario", "Bowser", "King K. Rool", "Ganondorf"], correct: 1 },
                { question: "Which game features the city of Rapture?", answers: ["Fallout", "BioShock", "Dishonored", "Prey"], correct: 1 },
                { question: "What is the currency in Fortnite?", answers: ["Coins", "V-Bucks", "Credits", "Gems"], correct: 1 },
                { question: "Which game series features Kratos?", answers: ["Devil May Cry", "God of War", "Bayonetta", "Ninja Gaiden"], correct: 1 },
                { question: "What is the highest level in Pac-Man?", answers: ["Level 255", "Level 256", "Level 999", "Level 1000"], correct: 1 },
                { question: "Which game introduced the concept of 'respawning'?", answers: ["Doom", "Quake", "Wolfenstein", "Duke Nukem"], correct: 0 },
                { question: "What is the name of the princess in the Super Mario series?", answers: ["Zelda", "Peach", "Daisy", "Rosalina"], correct: 1 },
                { question: "Which game features the character Solid Snake?", answers: ["Splinter Cell", "Metal Gear", "Hitman", "Assassin's Creed"], correct: 1 },
                { question: "What is the maximum level in the original Donkey Kong?", answers: ["Level 22", "Level 117", "Level 255", "Level 999"], correct: 1 },
                { question: "Which game popularized the zombie survival genre?", answers: ["Dead Rising", "Left 4 Dead", "Resident Evil", "Call of Duty: Zombies"], correct: 2 },
                { question: "What is the name of the main character in Half-Life?", answers: ["Gordon Freeman", "Adrian Shephard", "Barney Calhoun", "Alyx Vance"], correct: 0 },
                { question: "Which game features the character Lara Croft?", answers: ["Uncharted", "Tomb Raider", "Indiana Jones", "Assassin's Creed"], correct: 1 },
                { question: "What is the name of the final boss in the original Legend of Zelda?", answers: ["Ganon", "Ganondorf", "Agahnim", "Vaati"], correct: 0 },
                { question: "Which game introduced the concept of achievements/trophies?", answers: ["Xbox 360", "PlayStation 3", "Steam", "Nintendo Wii"], correct: 0 },
                { question: "What is the name of the AI in the Halo series?", answers: ["Cortana", "ARIA", "EDI", "GLaDOS"], correct: 0 },
                { question: "Which game features the character Samus Aran?", answers: ["Metroid", "Star Fox", "F-Zero", "Pikmin"], correct: 0 }
            ],
            music: [
                { question: "Which artist released the album 'Midnights' in 2022?", answers: ["Ariana Grande", "Taylor Swift", "Billie Eilish", "Dua Lipa"], correct: 1 },
                { question: "Who composed 'The Four Seasons'?", answers: ["Bach", "Mozart", "Vivaldi", "Beethoven"], correct: 2 },
                { question: "Which band released 'Bohemian Rhapsody'?", answers: ["The Beatles", "Led Zeppelin", "Queen", "The Rolling Stones"], correct: 2 },
                { question: "What instrument did Louis Armstrong famously play?", answers: ["Piano", "Trumpet", "Saxophone", "Trombone"], correct: 1 },
                { question: "Which artist is known as the 'King of Pop'?", answers: ["Elvis Presley", "Michael Jackson", "Prince", "David Bowie"], correct: 1 },
                { question: "What does 'BTS' stand for?", answers: ["Behind The Scenes", "Bangtan Sonyeondan", "Boys That Sing", "Beyond The Stars"], correct: 1 },
                { question: "Which song won the Grammy for Song of the Year in 2023?", answers: ["As It Was", "About Damn Time", "Bad Habit", "Unholy"], correct: 1 },
                { question: "Who wrote the musical 'Hamilton'?", answers: ["Stephen Sondheim", "Lin-Manuel Miranda", "Andrew Lloyd Webber", "Jonathan Larson"], correct: 1 },
                { question: "Which artist has won the most Grammy Awards?", answers: ["Michael Jackson", "Beyoncé", "Quincy Jones", "Alison Krauss"], correct: 1 },
                { question: "What genre of music did Bob Marley popularize?", answers: ["Jazz", "Blues", "Reggae", "Soul"], correct: 2 },
                { question: "Which Beatles album features 'Here Comes the Sun'?", answers: ["Abbey Road", "Sgt. Pepper's", "Revolver", "The White Album"], correct: 0 },
                { question: "Who is known as the 'Queen of Soul'?", answers: ["Diana Ross", "Aretha Franklin", "Whitney Houston", "Tina Turner"], correct: 1 },
                { question: "Which instrument has 88 keys?", answers: ["Organ", "Piano", "Harpsichord", "Accordion"], correct: 1 },
                { question: "What was Elvis Presley's middle name?", answers: ["Aaron", "Andrew", "Anthony", "Albert"], correct: 0 },
                { question: "Which artist released 'Purple Rain'?", answers: ["Michael Jackson", "Prince", "David Bowie", "George Michael"], correct: 1 },
                { question: "What does 'DJ' stand for?", answers: ["Dance Jockey", "Disc Jockey", "Digital Jockey", "Dynamic Jockey"], correct: 1 },
                { question: "Which band performed at the first Woodstock festival?", answers: ["The Who", "Jimi Hendrix", "Janis Joplin", "All of the above"], correct: 3 },
                { question: "Who composed 'The Magic Flute'?", answers: ["Bach", "Mozart", "Beethoven", "Haydn"], correct: 1 },
                { question: "Which artist is known for the song 'Imagine'?", answers: ["Paul McCartney", "John Lennon", "George Harrison", "Ringo Starr"], correct: 1 },
                { question: "What is the highest male singing voice?", answers: ["Tenor", "Baritone", "Bass", "Countertenor"], correct: 3 },
                { question: "Which artist released 'Thriller'?", answers: ["Prince", "Michael Jackson", "Stevie Wonder", "Marvin Gaye"], correct: 1 },
                { question: "What does 'R&B' stand for?", answers: ["Rock and Blues", "Rhythm and Blues", "Rap and Bass", "Retro and Beat"], correct: 1 },
                { question: "Which instrument is Yo-Yo Ma famous for playing?", answers: ["Violin", "Piano", "Cello", "Viola"], correct: 2 },
                { question: "Who wrote 'Stairway to Heaven'?", answers: ["The Beatles", "Led Zeppelin", "Pink Floyd", "The Rolling Stones"], correct: 1 },
                { question: "Which artist is known as 'The Boss'?", answers: ["Bob Dylan", "Bruce Springsteen", "Tom Petty", "Neil Young"], correct: 1 }
            ],
            soccer: [
                { question: "Who won the 2022 FIFA World Cup?", answers: ["France", "Argentina", "Brazil", "Croatia"], correct: 1 },
                { question: "Which player scored the most goals in the 2022 World Cup?", answers: ["Lionel Messi", "Kylian Mbappé", "Olivier Giroud", "Julian Alvarez"], correct: 1 },
                { question: "Who won the 2023 UEFA Champions League?", answers: ["Real Madrid", "Manchester City", "Inter Milan", "AC Milan"], correct: 1 },
                { question: "Which team won the 2023 Premier League?", answers: ["Arsenal", "Manchester City", "Manchester United", "Newcastle"], correct: 1 },
                { question: "Who is the current top scorer in Champions League history?", answers: ["Lionel Messi", "Cristiano Ronaldo", "Robert Lewandowski", "Karim Benzema"], correct: 1 },
                { question: "Which country will host the 2026 FIFA World Cup?", answers: ["Qatar", "Russia", "USA/Canada/Mexico", "Germany"], correct: 2 },
                { question: "Who won the 2023 Ballon d'Or?", answers: ["Lionel Messi", "Erling Haaland", "Kylian Mbappé", "Karim Benzema"], correct: 0 },
                { question: "Which club did Lionel Messi join in 2023?", answers: ["PSG", "Barcelona", "Inter Miami", "Manchester City"], correct: 2 },
                { question: "Who is the current manager of Manchester City?", answers: ["Jurgen Klopp", "Pep Guardiola", "Thomas Tuchel", "Antonio Conte"], correct: 1 },
                { question: "Which player transferred to Al-Nassr in 2023?", answers: ["Lionel Messi", "Cristiano Ronaldo", "Neymar", "Karim Benzema"], correct: 1 },
                { question: "Who won the 2023 Copa America?", answers: ["Brazil", "Argentina", "Uruguay", "Colombia"], correct: 1 },
                { question: "Which team won the 2023 FA Cup?", answers: ["Manchester City", "Manchester United", "Arsenal", "Chelsea"], correct: 0 },
                { question: "Who is the current top scorer in Premier League history?", answers: ["Wayne Rooney", "Alan Shearer", "Harry Kane", "Sergio Aguero"], correct: 1 },
                { question: "Which country won the 2023 Women's World Cup?", answers: ["USA", "England", "Spain", "Australia"], correct: 2 },
                { question: "Who is the current manager of Arsenal?", answers: ["Mikel Arteta", "Arsene Wenger", "Unai Emery", "Freddie Ljungberg"], correct: 0 },
                { question: "Which player has won the most Champions League titles?", answers: ["Lionel Messi", "Cristiano Ronaldo", "Sergio Ramos", "Karim Benzema"], correct: 1 },
                { question: "What is the maximum number of players on a soccer field for one team?", answers: ["10", "11", "12", "9"], correct: 1 },
                { question: "Which club has won the most Champions League titles?", answers: ["Barcelona", "AC Milan", "Real Madrid", "Liverpool"], correct: 2 },
                { question: "Who scored the 'Hand of God' goal?", answers: ["Pele", "Diego Maradona", "Ronaldinho", "Zinedine Zidane"], correct: 1 },
                { question: "Which country has won the most World Cups?", answers: ["Germany", "Argentina", "Brazil", "Italy"], correct: 2 },
                { question: "What is the duration of a standard soccer match?", answers: ["80 minutes", "90 minutes", "100 minutes", "120 minutes"], correct: 1 },
                { question: "Which player is known as 'CR7'?", answers: ["Cristiano Ronaldo", "Carlos Ruiz", "Claudio Ranieri", "Cesc Fabregas"], correct: 0 },
                { question: "What is the offside rule in soccer?", answers: ["Player behind last defender", "Player ahead of ball", "Player in penalty area", "Player touching ball with hands"], correct: 0 },
                { question: "Which tournament is held every 4 years for national teams?", answers: ["Champions League", "World Cup", "Copa America", "European Championship"], correct: 1 },
                { question: "Who is known as 'The Special One'?", answers: ["Pep Guardiola", "Jose Mourinho", "Carlo Ancelotti", "Jurgen Klopp"], correct: 1 }
            ]
        };

        // Game state
        const gameState = {
            currentScreen: 'landing-screen',
            currentQuestion: 0,
            score: 0,
            correctAnswers: 0,
            timer: null,
            timeLeft: 15,
            selectedCategory: 'general',
            questions: []
        };

        // DOM elements
        const elements = {
            screens: {
                landing: document.getElementById('landing-screen'),
                setup: document.getElementById('setup-screen'),
                join: document.getElementById('join-screen'),
                quiz: document.getElementById('quiz-screen'),
                results: document.getElementById('results-screen'),
                leaderboard: document.getElementById('leaderboard-screen')
            },
            quiz: {
                currentQuestion: document.getElementById('current-question'),
                totalQuestions: document.getElementById('total-questions'),
                questionText: document.getElementById('question-text'),
                answersContainer: document.getElementById('answers-container'),
                progressBar: document.getElementById('progress-bar'),
                timeLeft: document.getElementById('time-left'),
                currentScore: document.getElementById('current-score'),
                circle: document.querySelector('.progress-ring__circle'),
                finalScore: document.getElementById('final-score'),
                correctAnswers: document.getElementById('correct-answers'),
                timeTaken: document.getElementById('time-taken')
            }
        };

        // Initialize circle progress
        if (elements.quiz.circle) {
            const radius = elements.quiz.circle.r.baseVal.value;
            const circumference = radius * 2 * Math.PI;
            elements.quiz.circle.style.strokeDasharray = `${circumference} ${circumference}`;
            elements.quiz.circle.style.strokeDashoffset = circumference;
        }

        // Screen navigation
        function showScreen(screenId) {
            // Hide all screens
            Object.values(elements.screens).forEach(screen => {
                screen.classList.add('hidden');
            });
            
            // Show requested screen
            document.getElementById(screenId).classList.remove('hidden');
            gameState.currentScreen = screenId;
            
            // Special setup for certain screens
            if (screenId === 'quiz-screen') {
                loadQuestion();
            } else if (screenId === 'results-screen') {
                displayResults();
            }
        }

        // Theme switcher
        function setTheme(theme) {
            document.body.className = `theme-${theme} bg-[var(--bg)] min-h-screen text-white transition-colors duration-500`;
        }

        // Quiz functions
        function startQuiz() {
            // Get selected category
            const categorySelect = document.getElementById('category-select');
            gameState.selectedCategory = categorySelect ? categorySelect.value : 'general';

            // Get questions for selected category and shuffle them
            const categoryQuestions = questionDatabase[gameState.selectedCategory] || questionDatabase.general;
            gameState.questions = shuffleArray([...categoryQuestions]).slice(0, 10); // Take 10 random questions

            gameState.currentQuestion = 0;
            gameState.score = 0;
            gameState.correctAnswers = 0;
            elements.quiz.currentScore.textContent = '0';
            elements.quiz.totalQuestions.textContent = gameState.questions.length.toString();

            showScreen('quiz-screen');
        }

        // Utility function to shuffle array
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }

        function loadQuestion() {
            if (gameState.currentQuestion >= gameState.questions.length) {
                endQuiz();
                return;
            }
            
            const question = gameState.questions[gameState.currentQuestion];
            elements.quiz.currentQuestion.textContent = (gameState.currentQuestion + 1).toString();
            elements.quiz.questionText.textContent = question.question;
            
            // Update progress bar
            const progress = (gameState.currentQuestion / gameState.questions.length) * 100;
            elements.quiz.progressBar.style.width = `${progress}%`;
            
            // Clear previous answers
            elements.quiz.answersContainer.innerHTML = '';
            
            // Add new answers
            question.answers.forEach((answer, index) => {
                const answerElement = document.createElement('button');
                answerElement.className = 'arcade-button pixel-font bg-black hover:bg-[var(--primary)] text-white py-4 px-6 rounded-lg border-2 border-[var(--secondary)] transition-all duration-300 transform hover:scale-105';
                answerElement.textContent = answer;
                answerElement.onclick = () => checkAnswer(index);
                elements.quiz.answersContainer.appendChild(answerElement);
            });
            
            // Start timer
            startTimer();
        }

        function startTimer() {
            gameState.timeLeft = 15;
            elements.quiz.timeLeft.textContent = gameState.timeLeft.toString();
            
            if (elements.quiz.circle) {
                const radius = elements.quiz.circle.r.baseVal.value;
                const circumference = radius * 2 * Math.PI;
                const offset = circumference - (gameState.timeLeft / 15) * circumference;
                elements.quiz.circle.style.strokeDashoffset = offset;
            }
            
            clearInterval(gameState.timer);
            gameState.timer = setInterval(() => {
                gameState.timeLeft--;
                elements.quiz.timeLeft.textContent = gameState.timeLeft.toString();
                
                if (elements.quiz.circle) {
                    const radius = elements.quiz.circle.r.baseVal.value;
                    const circumference = radius * 2 * Math.PI;
                    const offset = circumference - (gameState.timeLeft / 15) * circumference;
                    elements.quiz.circle.style.strokeDashoffset = offset;
                }
                
                // Change color when time is running out
                if (gameState.timeLeft <= 5) {
                    elements.quiz.circle.style.stroke = 'var(--accent)';
                }
                
                if (gameState.timeLeft <= 0) {
                    clearInterval(gameState.timer);
                    timeUp();
                }
            }, 1000);
        }

        function checkAnswer(answerIndex) {
            clearInterval(gameState.timer);
            
            const question = gameState.questions[gameState.currentQuestion];
            const answerButtons = elements.quiz.answersContainer.querySelectorAll('button');
            
            // Disable all buttons
            answerButtons.forEach(button => {
                button.disabled = true;
            });
            
            // Mark correct answer
            answerButtons[question.correct].classList.add('answer-correct');
            
            // If wrong answer, mark it
            if (answerIndex !== question.correct) {
                answerButtons[answerIndex].classList.add('answer-wrong');
                answerButtons[answerIndex].classList.add('shake');
            } else {
                // Correct answer
                gameState.score += 100 * gameState.timeLeft;
                gameState.correctAnswers++;
                elements.quiz.currentScore.textContent = gameState.score.toString();
            }
            
            // Move to next question after delay
            setTimeout(() => {
                gameState.currentQuestion++;
                loadQuestion();
            }, 2000);
        }

        function timeUp() {
            const answerButtons = elements.quiz.answersContainer.querySelectorAll('button');
            const question = gameState.questions[gameState.currentQuestion];
            
            // Disable all buttons
            answerButtons.forEach(button => {
                button.disabled = true;
            });
            
            // Mark correct answer
            answerButtons[question.correct].classList.add('answer-correct');
            answerButtons[question.correct].classList.add('float');
            
            // Move to next question after delay
            setTimeout(() => {
                gameState.currentQuestion++;
                loadQuestion();
            }, 2000);
        }

        function endQuiz() {
            showScreen('results-screen');
        }

        function displayResults() {
            elements.quiz.finalScore.textContent = gameState.score.toString();
            elements.quiz.correctAnswers.textContent = `${gameState.correctAnswers}/${gameState.questions.length}`;

            // Calculate time taken (mock)
            const minutes = Math.floor(Math.random() * 2) + 1;
            const seconds = Math.floor(Math.random() * 60);
            elements.quiz.timeTaken.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

            // Create matrix rain for results screen
            createResultsMatrixRain();
        }

        function createResultsMatrixRain() {
            const container = document.getElementById('results-matrix');
            if (!container) return;

            // Clear existing matrix
            container.innerHTML = '';

            const characters = '★☆✦✧◆◇♦♢⚡⭐🎉🎊💫⭐';

            for (let i = 0; i < 20; i++) {
                const char = document.createElement('div');
                char.className = 'matrix-char';
                char.textContent = characters[Math.floor(Math.random() * characters.length)];
                char.style.left = Math.random() * 100 + '%';
                char.style.animationDelay = Math.random() * 3 + 's';
                char.style.animationDuration = (Math.random() * 2 + 3) + 's';
                char.style.color = ['gold', 'silver', '#ff6b6b', '#4ecdc4', '#ffe66d'][Math.floor(Math.random() * 5)];
                container.appendChild(char);
            }
        }

        // Visual Effects Functions
        function createMatrixRain() {
            const container = document.getElementById('matrix-container');
            if (!container) return;

            const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
            const columns = Math.floor(window.innerWidth / 20);

            for (let i = 0; i < 15; i++) {
                const char = document.createElement('div');
                char.className = 'matrix-char';
                char.textContent = characters[Math.floor(Math.random() * characters.length)];
                char.style.left = Math.random() * 100 + '%';
                char.style.animationDelay = Math.random() * 3 + 's';
                char.style.animationDuration = (Math.random() * 2 + 2) + 's';
                container.appendChild(char);
            }
        }

        function addCategoryIcons() {
            const categorySelect = document.getElementById('category-select');
            if (!categorySelect) return;

            const options = categorySelect.querySelectorAll('option');
            const icons = {
                'general': '🧠',
                'science': '🔬',
                'history': '🏛️',
                'movies': '🎬',
                'videogames': '🎮',
                'music': '🎵',
                'soccer': '⚽'
            };

            options.forEach(option => {
                const value = option.value;
                if (icons[value] && !option.textContent.includes(icons[value])) {
                    option.textContent = icons[value] + ' ' + option.textContent;
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            createMatrixRain();
            addCategoryIcons();
        });

        showScreen('landing-screen');
    </script>
</body>
</html>