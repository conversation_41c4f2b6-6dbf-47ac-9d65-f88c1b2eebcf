<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Quiz Arcade</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Rubik+Mono+One&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes flicker {
            0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
                text-shadow:
                    0 0 5px #fff,
                    0 0 10px #fff,
                    0 0 20px #0fa,
                    0 0 40px #0fa,
                    0 0 80px #0fa;
            }
            20%, 24%, 55% {        
                text-shadow: none;
            }
        }
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.8);
            }
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) rotate(-2deg); }
            20%, 40%, 60%, 80% { transform: translateX(5px) rotate(2deg); }
        }
        @keyframes progress-pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }
        @keyframes explode {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }
        .animate-progress {
            animation: progress-pulse 1.5s infinite;
        }
        .neon-text {
            animation: flicker 3s infinite alternate;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-stroke: 1px rgba(255,255,255,0.5);
            -webkit-text-stroke: 1px rgba(255,255,255,0.5);
        }
        .pulse-button {
            animation: pulse 2s infinite;
        }
        .float {
            animation: float 3s ease-in-out infinite;
        }
        .shake {
            animation: shake 0.5s;
        }
        .pixel-border {
            border-style: solid;
            border-width: 4px;
            border-image: repeating-linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff, #00ffff) 10;
        }
        .arcade-screen {
            background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.3);
        }
        .theme-neon {
            --primary: #ff00ff;
            --secondary: #00ffff;
            --accent: #ffcc00;
            --bg: #1a1a2e;
        }
        .theme-pixel {
            --primary: #ff3366;
            --secondary: #66ff33;
            --accent: #3366ff;
            --bg: #222;
        }
        .theme-synth {
            --primary: #ff6b6b;
            --secondary: #4ecdc4;
            --accent: #ffe66d;
            --bg: #0f0c29;
        }
        .answer-correct {
            background-color: rgba(0, 255, 0, 0.3);
            border: 2px solid #00ff00;
            animation: pulse 0.5s 3, float 1s ease-in-out;
            box-shadow: 0 0 20px #00ff00;
        }
        .answer-wrong {
            background-color: rgba(255, 0, 0, 0.3);
            border: 2px solid #ff0000;
            animation: shake 0.5s, pulse 0.5s 3;
            box-shadow: 0 0 20px #ff0000;
        }
        .arcade-button {
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(255,255,255,0.2);
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
        .arcade-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            transition: all 0.5s;
        }
        .arcade-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255,255,255,0.4);
        }
        .arcade-button:hover::before {
            left: 100%;
        }
        .arcade-button:active {
            transform: translateY(1px);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring__circle {
            transition: stroke-dashoffset 0.5s;
            stroke-linecap: round;
        }
        .pixel-font {
            font-family: 'Press Start 2P', cursive;
        }
        .retro-font {
            font-family: 'Rubik Mono One', sans-serif;
        }
    </style>
</head>
<body class="theme-neon bg-[var(--bg)] min-h-screen text-white transition-colors duration-500">
    <div id="app" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Landing Screen -->
        <div id="landing-screen" class="arcade-screen w-full max-w-4xl rounded-lg p-8 text-center relative overflow-hidden">
            <div class="absolute inset-0 bg-black opacity-20"></div>
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[var(--primary)] to-[var(--secondary)]"></div>
            <div class="relative z-10">
                <h1 class="neon-text retro-font text-5xl md:text-7xl mb-6">RETRO QUIZ ARCADE</h1>
                <p class="pixel-font text-xl mb-12">Test your knowledge in this electrifying trivia challenge!</p>
                
                <div class="flex flex-col md:flex-row justify-center gap-6 mb-12">
                    <button onclick="showScreen('setup-screen')" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        START GAME
                    </button>
                    <button onclick="showScreen('join-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        JOIN GAME
                    </button>
                    <button onclick="showScreen('leaderboard-screen')" class="arcade-button pixel-font bg-[var(--accent)] hover:bg-[var(--primary)] text-black py-4 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        LEADERBOARD
                    </button>
                </div>
                
                <div class="flex justify-center gap-4 mb-4">
                    <span class="pixel-font">THEME:</span>
                    <button onclick="setTheme('neon')" class="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-blue-500"></button>
                    <button onclick="setTheme('pixel')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-500 to-green-500"></button>
                    <button onclick="setTheme('synth')" class="w-8 h-8 rounded-full bg-gradient-to-br from-red-400 to-teal-400"></button>
                </div>
            </div>
            
            <div class="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                <div class="w-3 h-3 rounded-full bg-[var(--primary)] animate-pulse"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--secondary)] animate-pulse delay-100"></div>
                <div class="w-3 h-3 rounded-full bg-[var(--accent)] animate-pulse delay-200"></div>
            </div>
        </div>
        
        <!-- Setup Screen -->
        <div id="setup-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">GAME SETUP</h2>
                <p class="pixel-font text-lg">Customize your quiz experience</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">CATEGORY</h3>
                    <select class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]">
                        <option>General Knowledge</option>
                        <option>Science & Nature</option>
                        <option>History</option>
                        <option>Movies</option>
                        <option>Video Games</option>
                        <option>Music</option>
                    </select>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--secondary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--secondary)]">DIFFICULTY</h3>
                    <div class="flex justify-between">
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" checked class="form-radio h-5 w-5 text-[var(--primary)]">
                            <span class="ml-2 pixel-font">Easy</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--secondary)]">
                            <span class="ml-2 pixel-font">Medium</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="difficulty" class="form-radio h-5 w-5 text-[var(--accent)]">
                            <span class="ml-2 pixel-font">Hard</span>
                        </label>
                    </div>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--accent)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--accent)]">QUESTIONS</h3>
                    <input type="range" min="5" max="20" value="10" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-[var(--primary)]">
                    <div class="flex justify-between mt-2">
                        <span class="pixel-font">5</span>
                        <span class="pixel-font">10</span>
                        <span class="pixel-font">15</span>
                        <span class="pixel-font">20</span>
                    </div>
                </div>
                
                <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                    <h3 class="pixel-font text-xl mb-4 text-[var(--primary)]">TIME PER QUESTION</h3>
                    <div class="flex items-center gap-4">
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">-</button>
                        <span class="pixel-font text-2xl">15</span>
                        <span class="pixel-font">seconds</span>
                        <button class="pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-black py-2 px-4 rounded-lg">+</button>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <button onclick="startQuiz()" class="arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-4 px-12 rounded-lg text-2xl transition-all duration-300 transform hover:scale-105">
                    START QUIZ
                </button>
            </div>
        </div>
        
        <!-- Join Screen -->
        <div id="join-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">JOIN GAME</h2>
                <p class="pixel-font text-lg">Enter a game code to join</p>
            </div>
            
            <div class="max-w-md mx-auto bg-black bg-opacity-30 p-8 rounded-lg border-2 border-[var(--secondary)]">
                <div class="mb-6">
                    <label class="block pixel-font text-lg mb-2 text-[var(--primary)]">GAME CODE</label>
                    <input type="text" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]" placeholder="Enter 6-digit code">
                </div>
                
                <div class="mb-6">
                    <label class="block pixel-font text-lg mb-2 text-[var(--primary)]">PLAYER NAME</label>
                    <input type="text" class="w-full pixel-font bg-black text-white p-3 rounded-lg border-2 border-[var(--secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent)]" placeholder="Your nickname">
                </div>
                
                <button class="w-full arcade-button pulse-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                    JOIN NOW
                </button>
            </div>
        </div>
        
        <!-- Quiz Screen -->
        <div id="quiz-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <div class="flex justify-between items-center mb-8">
                <div class="pixel-font text-lg">
                    QUESTION <span id="current-question">1</span>/<span id="total-questions">10</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 relative">
                        <svg class="progress-ring w-8 h-8" viewBox="0 0 36 36">
                            <circle class="progress-ring__circle" stroke="var(--primary)" stroke-width="3" fill="transparent" r="16" cx="18" cy="18"></circle>
                        </svg>
                        <span id="time-left" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pixel-font text-xs">15</span>
                    </div>
                    <span class="pixel-font">SCORE: <span id="current-score">0</span></span>
                </div>
            </div>
            
            <div id="question-container" class="bg-black bg-opacity-30 p-6 mb-8 rounded-lg border-2 border-[var(--primary)] min-h-32 flex items-center justify-center">
                <h3 id="question-text" class="pixel-font text-xl md:text-2xl text-center">Loading question...</h3>
            </div>
            
            <div id="answers-container" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <!-- Answers will be inserted here by JavaScript -->
            </div>
            
            <div class="w-full bg-gray-800 rounded-full h-4 mb-4 overflow-hidden">
                <div id="progress-bar" class="bg-[var(--primary)] h-4 rounded-full relative" style="width: 0%">
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent to-white opacity-30 animate-progress"></div>
                </div>
            </div>
        </div>
        
        <!-- Results Screen -->
        <div id="results-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden text-center">
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="w-full h-full bg-[radial-gradient(circle,var(--primary),transparent)] opacity-20"></div>
            </div>
            
            <div class="relative z-10">
                <h2 class="neon-text retro-font text-5xl mb-6">GAME OVER!</h2>
                
                <div class="bg-black bg-opacity-50 p-8 rounded-lg border-2 border-[var(--secondary)] max-w-md mx-auto mb-8">
                    <div class="flex justify-between mb-4">
                        <div>
                            <div class="pixel-font text-sm">SCORE</div>
                            <div id="final-score" class="text-4xl font-bold text-[var(--primary)]">850</div>
                        </div>
                        <div>
                            <div class="pixel-font text-sm">CORRECT</div>
                            <div id="correct-answers" class="text-4xl font-bold text-[var(--secondary)]">8/10</div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="pixel-font text-sm">TIME</div>
                        <div id="time-taken" class="text-xl text-[var(--accent)]">2:15</div>
                    </div>
                    
                    <div class="flex justify-center gap-4 mt-6 relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-32 h-32 rounded-full bg-[var(--primary)] opacity-10 animate-ping absolute"></div>
                        </div>
                        <div class="float z-10">
                            <i class="fas fa-trophy text-4xl text-yellow-400 drop-shadow-lg"></i>
                        </div>
                        <div class="float animation-delay-200 z-10">
                            <i class="fas fa-medal text-4xl text-blue-400 drop-shadow-lg"></i>
                        </div>
                        <div class="float animation-delay-400 z-10">
                            <i class="fas fa-star text-4xl text-pink-400 drop-shadow-lg"></i>
                        </div>
                    </div>
                    <div class="absolute inset-0 overflow-hidden">
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                        <div class="confetti"></div>
                    </div>
                </div>
                
                <div class="flex flex-col md:flex-row justify-center gap-6">
                    <button onclick="startQuiz()" class="arcade-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        PLAY AGAIN
                    </button>
                    <button onclick="showScreen('leaderboard-screen')" class="arcade-button pixel-font bg-[var(--secondary)] hover:bg-[var(--primary)] text-white py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        LEADERBOARD
                    </button>
                    <button class="arcade-button pixel-font bg-[var(--accent)] hover:bg-[var(--primary)] text-black py-3 px-6 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                        SHARE SCORE
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Leaderboard Screen -->
        <div id="leaderboard-screen" class="hidden arcade-screen w-full max-w-4xl rounded-lg p-8 relative overflow-hidden">
            <button onclick="showScreen('landing-screen')" class="absolute top-4 left-4 pixel-font bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                <i class="fas fa-arrow-left"></i> BACK
            </button>
            
            <div class="text-center mb-8">
                <h2 class="neon-text retro-font text-4xl mb-4">LEADERBOARD</h2>
                <p class="pixel-font text-lg">Top players this week</p>
            </div>
            
            <div class="bg-black bg-opacity-30 p-6 rounded-lg border-2 border-[var(--primary)]">
                <div class="grid grid-cols-12 gap-4 pixel-font mb-4 pb-2 border-b-2 border-[var(--secondary)]">
                    <div class="col-span-1">#</div>
                    <div class="col-span-6">PLAYER</div>
                    <div class="col-span-3">SCORE</div>
                    <div class="col-span-2">DATE</div>
                </div>
                
                <div class="space-y-3">
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--primary)] bg-opacity-20 p-2 rounded">
                        <div class="col-span-1 text-yellow-400">1</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-crown text-yellow-400"></i>
                            <span>RetroGamer88</span>
                        </div>
                        <div class="col-span-3">980</div>
                        <div class="col-span-2 text-sm">Today</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--secondary)] bg-opacity-10 p-2 rounded">
                        <div class="col-span-1 text-gray-300">2</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-medal text-gray-300"></i>
                            <span>PixelMaster</span>
                        </div>
                        <div class="col-span-3">950</div>
                        <div class="col-span-2 text-sm">Yesterday</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center bg-[var(--accent)] bg-opacity-10 p-2 rounded">
                        <div class="col-span-1 text-amber-600">3</div>
                        <div class="col-span-6 flex items-center gap-2">
                            <i class="fas fa-medal text-amber-600"></i>
                            <span>NeonNinja</span>
                        </div>
                        <div class="col-span-3">920</div>
                        <div class="col-span-2 text-sm">2 days ago</div>
                    </div>
                    
                    <!-- More leaderboard entries -->
                    <div class="grid grid-cols-12 gap-4 items-center p-2 rounded hover:bg-black hover:bg-opacity-20">
                        <div class="col-span-1">4</div>
                        <div class="col-span-6">QuizWizard</div>
                        <div class="col-span-3">890</div>
                        <div class="col-span-2 text-sm">3 days ago</div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-4 items-center p-2 rounded hover:bg-black hover:bg-opacity-20">
                        <div class="col-span-1">5</div>
                        <div class="col-span-6">ArcadeAce</div>
                        <div class="col-span-3">870</div>
                        <div class="col-span-2 text-sm">4 days ago</div>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 flex justify-center">
                <button onclick="showScreen('landing-screen')" class="arcade-button pixel-font bg-[var(--primary)] hover:bg-[var(--secondary)] text-white py-3 px-8 rounded-lg text-xl transition-all duration-300 transform hover:scale-105">
                    MAIN MENU
                </button>
            </div>
        </div>
    </div>

    <script>
        // Game state
        const gameState = {
            currentScreen: 'landing-screen',
            currentQuestion: 0,
            score: 0,
            correctAnswers: 0,
            timer: null,
            timeLeft: 15,
            questions: [
                {
                    question: "What was the first video game console?",
                    answers: ["Atari 2600", "Magnavox Odyssey", "Nintendo Entertainment System", "Sega Genesis"],
                    correct: 1
                },
                {
                    question: "Which of these is NOT a programming language?",
                    answers: ["Python", "Java", "HTML", "Ruby"],
                    correct: 2
                },
                {
                    question: "What year was the internet created?",
                    answers: ["1969", "1983", "1991", "1975"],
                    correct: 0
                },
                {
                    question: "Which company created the first smartphone?",
                    answers: ["Apple", "IBM", "Nokia", "BlackBerry"],
                    correct: 1
                },
                {
                    question: "What does 'HTTP' stand for?",
                    answers: ["HyperText Transfer Protocol", "High-Tech Text Protocol", "Hyper Transfer Text Protocol", "High Transfer Text Protocol"],
                    correct: 0
                }
            ]
        };

        // DOM elements
        const elements = {
            screens: {
                landing: document.getElementById('landing-screen'),
                setup: document.getElementById('setup-screen'),
                join: document.getElementById('join-screen'),
                quiz: document.getElementById('quiz-screen'),
                results: document.getElementById('results-screen'),
                leaderboard: document.getElementById('leaderboard-screen')
            },
            quiz: {
                currentQuestion: document.getElementById('current-question'),
                totalQuestions: document.getElementById('total-questions'),
                questionText: document.getElementById('question-text'),
                answersContainer: document.getElementById('answers-container'),
                progressBar: document.getElementById('progress-bar'),
                timeLeft: document.getElementById('time-left'),
                currentScore: document.getElementById('current-score'),
                circle: document.querySelector('.progress-ring__circle'),
                finalScore: document.getElementById('final-score'),
                correctAnswers: document.getElementById('correct-answers'),
                timeTaken: document.getElementById('time-taken')
            }
        };

        // Initialize circle progress
        if (elements.quiz.circle) {
            const radius = elements.quiz.circle.r.baseVal.value;
            const circumference = radius * 2 * Math.PI;
            elements.quiz.circle.style.strokeDasharray = `${circumference} ${circumference}`;
            elements.quiz.circle.style.strokeDashoffset = circumference;
        }

        // Screen navigation
        function showScreen(screenId) {
            // Hide all screens
            Object.values(elements.screens).forEach(screen => {
                screen.classList.add('hidden');
            });
            
            // Show requested screen
            document.getElementById(screenId).classList.remove('hidden');
            gameState.currentScreen = screenId;
            
            // Special setup for certain screens
            if (screenId === 'quiz-screen') {
                loadQuestion();
            } else if (screenId === 'results-screen') {
                displayResults();
            }
        }

        // Theme switcher
        function setTheme(theme) {
            document.body.className = `theme-${theme} bg-[var(--bg)] min-h-screen text-white transition-colors duration-500`;
        }

        // Quiz functions
        function startQuiz() {
            gameState.currentQuestion = 0;
            gameState.score = 0;
            gameState.correctAnswers = 0;
            elements.quiz.currentScore.textContent = '0';
            elements.quiz.totalQuestions.textContent = gameState.questions.length.toString();
            
            showScreen('quiz-screen');
        }

        function loadQuestion() {
            if (gameState.currentQuestion >= gameState.questions.length) {
                endQuiz();
                return;
            }
            
            const question = gameState.questions[gameState.currentQuestion];
            elements.quiz.currentQuestion.textContent = (gameState.currentQuestion + 1).toString();
            elements.quiz.questionText.textContent = question.question;
            
            // Update progress bar
            const progress = (gameState.currentQuestion / gameState.questions.length) * 100;
            elements.quiz.progressBar.style.width = `${progress}%`;
            
            // Clear previous answers
            elements.quiz.answersContainer.innerHTML = '';
            
            // Add new answers
            question.answers.forEach((answer, index) => {
                const answerElement = document.createElement('button');
                answerElement.className = 'arcade-button pixel-font bg-black hover:bg-[var(--primary)] text-white py-4 px-6 rounded-lg border-2 border-[var(--secondary)] transition-all duration-300 transform hover:scale-105';
                answerElement.textContent = answer;
                answerElement.onclick = () => checkAnswer(index);
                elements.quiz.answersContainer.appendChild(answerElement);
            });
            
            // Start timer
            startTimer();
        }

        function startTimer() {
            gameState.timeLeft = 15;
            elements.quiz.timeLeft.textContent = gameState.timeLeft.toString();
            
            if (elements.quiz.circle) {
                const radius = elements.quiz.circle.r.baseVal.value;
                const circumference = radius * 2 * Math.PI;
                const offset = circumference - (gameState.timeLeft / 15) * circumference;
                elements.quiz.circle.style.strokeDashoffset = offset;
            }
            
            clearInterval(gameState.timer);
            gameState.timer = setInterval(() => {
                gameState.timeLeft--;
                elements.quiz.timeLeft.textContent = gameState.timeLeft.toString();
                
                if (elements.quiz.circle) {
                    const radius = elements.quiz.circle.r.baseVal.value;
                    const circumference = radius * 2 * Math.PI;
                    const offset = circumference - (gameState.timeLeft / 15) * circumference;
                    elements.quiz.circle.style.strokeDashoffset = offset;
                }
                
                // Change color when time is running out
                if (gameState.timeLeft <= 5) {
                    elements.quiz.circle.style.stroke = 'var(--accent)';
                }
                
                if (gameState.timeLeft <= 0) {
                    clearInterval(gameState.timer);
                    timeUp();
                }
            }, 1000);
        }

        function checkAnswer(answerIndex) {
            clearInterval(gameState.timer);
            
            const question = gameState.questions[gameState.currentQuestion];
            const answerButtons = elements.quiz.answersContainer.querySelectorAll('button');
            
            // Disable all buttons
            answerButtons.forEach(button => {
                button.disabled = true;
            });
            
            // Mark correct answer
            answerButtons[question.correct].classList.add('answer-correct');
            
            // If wrong answer, mark it
            if (answerIndex !== question.correct) {
                answerButtons[answerIndex].classList.add('answer-wrong');
                answerButtons[answerIndex].classList.add('shake');
            } else {
                // Correct answer
                gameState.score += 100 * gameState.timeLeft;
                gameState.correctAnswers++;
                elements.quiz.currentScore.textContent = gameState.score.toString();
            }
            
            // Move to next question after delay
            setTimeout(() => {
                gameState.currentQuestion++;
                loadQuestion();
            }, 2000);
        }

        function timeUp() {
            const answerButtons = elements.quiz.answersContainer.querySelectorAll('button');
            const question = gameState.questions[gameState.currentQuestion];
            
            // Disable all buttons
            answerButtons.forEach(button => {
                button.disabled = true;
            });
            
            // Mark correct answer
            answerButtons[question.correct].classList.add('answer-correct');
            answerButtons[question.correct].classList.add('float');
            
            // Move to next question after delay
            setTimeout(() => {
                gameState.currentQuestion++;
                loadQuestion();
            }, 2000);
        }

        function endQuiz() {
            showScreen('results-screen');
        }

        function displayResults() {
            elements.quiz.finalScore.textContent = gameState.score.toString();
            elements.quiz.correctAnswers.textContent = `${gameState.correctAnswers}/${gameState.questions.length}`;
            
            // Calculate time taken (mock)
            const minutes = Math.floor(Math.random() * 2) + 1;
            const seconds = Math.floor(Math.random() * 60);
            elements.quiz.timeTaken.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // Initialize
        showScreen('landing-screen');
    </script>
</body>
</html>