/* Root Variables */
:root {
    --primary: #ff00ff;
    --secondary: #00ffff;
    --accent: #ffcc00;
    --bg: #1a1a2e;
}

/* Base Styles */
body {
    background: var(--bg) !important;
    color: white !important;
    min-height: 100vh !important;
    font-family: 'Press Start 2P', cursive !important;
    margin: 0 !important;
    padding: 0 !important;
}

#app {
    background: var(--bg) !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
}

/* Enhanced Graphics and Animations */
@keyframes flicker {
    0%, 19%, 21%, 23%, 25%, 54%, 56%, 100% {
        text-shadow:
            0 0 5px #fff,
            0 0 10px #fff,
            0 0 20px #0fa,
            0 0 40px #0fa,
            0 0 80px #0fa;
    }
    20%, 24%, 55% {        
        text-shadow: none;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0.8);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px) rotate(-2deg); }
    20%, 40%, 60%, 80% { transform: translateX(5px) rotate(2deg); }
}

@keyframes progress-pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

@keyframes explode {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

@keyframes matrix-rain {
    0% { transform: translateY(-100vh); opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes hologram {
    0%, 100% { 
        transform: skew(0deg, 0deg);
        filter: hue-rotate(0deg);
    }
    25% { 
        transform: skew(1deg, 0deg);
        filter: hue-rotate(90deg);
    }
    50% { 
        transform: skew(0deg, 1deg);
        filter: hue-rotate(180deg);
    }
    75% { 
        transform: skew(-1deg, 0deg);
        filter: hue-rotate(270deg);
    }
}

@keyframes scan-lines {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100vh); }
}

@keyframes glow-pulse {
    0%, 100% { 
        box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
    }
    50% { 
        box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary), 0 0 40px var(--primary);
    }
}

@keyframes particle-float {
    0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

@keyframes lightning {
    0%, 90%, 100% { opacity: 0; }
    5%, 85% { opacity: 1; }
}

/* Animation Classes */
.animate-progress {
    animation: progress-pulse 1.5s infinite;
}

.neon-text {
    animation: flicker 3s infinite alternate;
    text-transform: uppercase;
    letter-spacing: 2px;
    -webkit-text-stroke: 1px rgba(255,255,255,0.5);
}

.pulse-button {
    animation: pulse 2s infinite;
}

.float {
    animation: float 3s ease-in-out infinite;
}

.shake {
    animation: shake 0.5s;
}

.hologram-effect {
    animation: hologram 4s infinite;
}

.glow-border {
    animation: glow-pulse 2s infinite;
    border: 2px solid var(--primary);
}

/* Visual Effect Elements */
.matrix-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.matrix-char {
    position: absolute;
    color: var(--primary);
    font-family: 'Press Start 2P', monospace;
    font-size: 12px;
    animation: matrix-rain 3s linear infinite;
    opacity: 0.3;
}

.scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--secondary), transparent);
    animation: scan-lines 2s linear infinite;
    opacity: 0.5;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent);
    border-radius: 50%;
    animation: particle-float 4s infinite;
}

.lightning-effect {
    position: absolute;
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, var(--secondary), transparent);
    animation: lightning 3s infinite;
}

.category-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 1.2em;
    animation: float 2s ease-in-out infinite;
}

/* Theme Variables */
body.theme-neon {
    --primary: #ff00ff;
    --secondary: #00ffff;
    --accent: #ffcc00;
    --bg: #1a1a2e;
    background: var(--bg);
}

body.theme-pixel {
    --primary: #ff3366;
    --secondary: #66ff33;
    --accent: #3366ff;
    --bg: #222;
    background: var(--bg);
}

body.theme-synth {
    --primary: #ff6b6b;
    --secondary: #4ecdc4;
    --accent: #ffe66d;
    --bg: #0f0c29;
    background: var(--bg);
}

/* Arcade Elements */
.pixel-border {
    border-style: solid;
    border-width: 4px;
    border-image: repeating-linear-gradient(45deg, #ff00ff, #00ffff, #ff00ff, #00ffff) 10;
}

.arcade-screen {
    background: radial-gradient(circle at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%) !important;
    box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.3) !important;
    border-radius: 0.5rem !important;
    padding: 2rem !important;
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    max-width: 64rem !important;
    text-align: center !important;
}

.screen-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    pointer-events: none;
}

.screen-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary), var(--secondary));
}

.arcade-button {
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(255,255,255,0.2);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.arcade-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255,255,255,0.4),
        transparent
    );
    transition: all 0.5s;
}

.arcade-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255,255,255,0.4);
}

.arcade-button:hover::before {
    left: 100%;
}

.arcade-button:active {
    transform: translateY(1px);
}

/* Answer States */
.answer-correct {
    background-color: rgba(0, 255, 0, 0.3) !important;
    border: 2px solid #00ff00 !important;
    animation: pulse 0.5s 3, float 1s ease-in-out !important;
    box-shadow: 0 0 20px #00ff00 !important;
}

.answer-wrong {
    background-color: rgba(255, 0, 0, 0.3) !important;
    border: 2px solid #ff0000 !important;
    animation: shake 0.5s, pulse 0.5s 3 !important;
    box-shadow: 0 0 20px #ff0000 !important;
}

/* Progress Ring */
.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring__circle {
    transition: stroke-dashoffset 0.5s;
    stroke-linecap: round;
}

/* Fonts */
.pixel-font {
    font-family: 'Press Start 2P', cursive !important;
    font-size: inherit !important;
}

.retro-font {
    font-family: 'Rubik Mono One', sans-serif !important;
    font-size: inherit !important;
}

/* Additional utility classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center !important;
}

.relative {
    position: relative !important;
}

.z-10 {
    z-index: 10 !important;
}

/* Main Content Layout */
.main-content {
    position: relative;
    z-index: 10;
}

.neon-text {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
    align-items: center;
}

@media (min-width: 768px) {
    .button-group {
        flex-direction: row;
        justify-content: center;
    }

    .neon-text {
        font-size: 5rem;
    }
}

.primary-btn {
    background: var(--primary) !important;
    color: white !important;
}

.secondary-btn {
    background: var(--secondary) !important;
    color: white !important;
}

.accent-btn {
    background: var(--accent) !important;
    color: black !important;
}

.theme-selector {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.theme-btn {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.theme-btn:hover {
    transform: scale(1.1);
}

.neon-theme {
    background: linear-gradient(45deg, #ff00ff, #00ffff);
}

.pixel-theme {
    background: linear-gradient(45deg, #ff3366, #66ff33);
}

.synth-theme {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

/* Bottom Indicators */
.bottom-indicators {
    position: absolute;
    bottom: 1rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.primary-indicator {
    background: var(--primary);
}

.secondary-indicator {
    background: var(--secondary);
    animation-delay: 0.1s;
}

.accent-indicator {
    background: var(--accent);
    animation-delay: 0.2s;
}

/* Screen Elements */
.back-button {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #666;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.back-button:hover {
    background: #555;
}

.screen-header {
    text-align: center;
    margin-bottom: 2rem;
}

.screen-header .neon-text {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.screen-header p {
    font-size: 1rem;
}

/* Setup Screen */
.setup-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 768px) {
    .setup-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.setup-card {
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 2px solid;
}

.primary-border {
    border-color: var(--primary);
}

.secondary-border {
    border-color: var(--secondary);
}

.accent-border {
    border-color: var(--accent);
}

.card-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.primary-text {
    color: var(--primary);
}

.secondary-text {
    color: var(--secondary);
}

.accent-text {
    color: var(--accent);
}

.game-select {
    width: 100%;
    background: black;
    color: white;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 2px solid var(--secondary);
    font-size: 0.8rem;
}

.game-select:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px rgba(255, 204, 0, 0.3);
}

/* Difficulty Options */
.difficulty-options {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.difficulty-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.difficulty-radio {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
    accent-color: var(--primary);
}

/* Question Slider */
.question-slider {
    width: 100%;
    height: 0.5rem;
    background: #333;
    border-radius: 0.25rem;
    outline: none;
    margin-bottom: 0.5rem;
}

.question-slider::-webkit-slider-thumb {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    background: var(--primary);
    cursor: pointer;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
}

/* Time Controls */
.time-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.time-btn {
    background: var(--secondary);
    color: black;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.time-btn:hover {
    background: var(--primary);
    color: white;
}

.time-display {
    font-size: 1.5rem;
}

/* Start Section */
.start-section {
    text-align: center;
}

.start-quiz-btn {
    background: var(--primary);
    color: white;
    padding: 1rem 3rem;
    font-size: 1.2rem;
}
